# 评估进度API安全性修改 - 前端集成指南

## 📋 概述

本文档详细说明了 `/api/v1/evaluations/progress/{assessmentId}` 端点的安全性修改对前端集成的影响，以及相应的解决方案。

**修改日期**: 2025-07-25  
**影响范围**: 前端UI组件、权限系统、错误处理  
**紧急程度**: 🚨 高优先级（会破坏现有功能）

---

## 🔧 API变更详情

### 端点信息
- **路径**: `GET /api/v1/evaluations/progress/{assessmentId}`
- **用途**: 获取考核评分进度统计
- **修改类型**: 权限限制 + 数据过滤

### 权限变更

| 项目 | 修改前 | 修改后 | 影响 |
|------|--------|--------|------|
| 允许角色 | `leader`, `boss`, `admin` | **仅** `leader` | 🚨 `boss`和`admin`角色失去访问权限 |
| 数据范围 | 可能包含所有参与者 | **仅**当前领导所在部门的参与者 | 📊 数据范围缩小 |
| 部门要求 | 无明确要求 | **必须**分配到具体部门 | ⚠️ 未分配部门的用户无法访问 |

---

## 🚨 破坏性变更分析

### 1. 403 Forbidden 错误

**受影响的角色**: `admin`, `boss`

```typescript
// 原有代码 - 现在会失败
const getEvaluationProgress = async (assessmentId: number) => {
  try {
    const response = await api.get(`/evaluations/progress/${assessmentId}`);
    return response.data;
  } catch (error) {
    // 现在会收到 403 Forbidden
    if (error.status === 403) {
      throw new Error('权限不足：只有领导角色可以访问');
    }
  }
};
```

### 2. 数据范围变化

**影响**: 统计数据和参与者列表现在只包含部门内成员

```typescript
// 数据结构未变，但内容范围缩小
interface EvaluationProgressDto {
  assessment_id: number;
  assessment_title: string;
  total_participants: number;        // 🔄 现在只计算部门内参与者
  self_completed_count: number;      // 🔄 部门范围统计
  leader_completed_count: number;    // 🔄 部门范围统计
  boss_completed_count: number;      // 🔄 部门范围统计
  participants: Array<{              // 🔄 只包含本部门成员
    user_id: number;
    user_name: string;
    department: string;              // 现在都是同一个部门
    // ... 其他字段
  }>;
  // ... 其他字段
}
```

---

## 🔨 前端修复方案

### 1. 权限检查组件

创建专用的权限守卫组件：

```typescript
// components/guards/EvaluationProgressGuard.tsx
import React from 'react';
import { useAuth } from '@/hooks/useAuth';
import { PermissionDenied } from '@/components/common/PermissionDenied';
import { DepartmentRequired } from '@/components/common/DepartmentRequired';

interface Props {
  children: React.ReactNode;
}

export const EvaluationProgressGuard: React.FC<Props> = ({ children }) => {
  const { user } = useAuth();

  // 检查角色权限
  if (!user.roles.includes('leader')) {
    return (
      <PermissionDenied 
        title="权限不足"
        message="只有领导角色可以查看评估进度"
        actionText="联系管理员"
        onAction={() => window.location.href = '/contact-admin'}
      />
    );
  }

  // 检查部门分配
  if (!user.department?.id) {
    return (
      <DepartmentRequired 
        title="部门信息缺失"
        message="您还未分配到具体部门，无法查看评估进度"
        actionText="联系HR"
        onAction={() => window.location.href = '/contact-hr'}
      />
    );
  }

  return <>{children}</>;
};
```

### 2. 路由保护

更新路由配置，增加权限检查：

```typescript
// router/evaluationRoutes.tsx
import { EvaluationProgressGuard } from '@/components/guards/EvaluationProgressGuard';

export const evaluationRoutes = [
  {
    path: '/evaluations/progress/:assessmentId',
    element: (
      <EvaluationProgressGuard>
        <EvaluationProgressPage />
      </EvaluationProgressGuard>
    ),
  },
];
```

### 3. 导航菜单条件渲染

```typescript
// components/navigation/NavigationMenu.tsx
import { useAuth } from '@/hooks/useAuth';

export const NavigationMenu = () => {
  const { user } = useAuth();
  
  const canAccessEvaluationProgress = 
    user.roles.includes('leader') && user.department?.id;

  return (
    <nav>
      {canAccessEvaluationProgress && (
        <NavItem 
          to="/evaluations/progress" 
          icon="📊"
          text="评估进度"
        />
      )}
      {/* 其他菜单项 */}
    </nav>
  );
};
```

### 4. 错误处理增强

```typescript
// hooks/useEvaluationProgress.ts
import { useState, useEffect } from 'react';
import { useNotification } from '@/hooks/useNotification';

interface ErrorResponse {
  status: number;
  message: string;
}

export const useEvaluationProgress = (assessmentId: number) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { showError } = useNotification();

  const handleApiError = (error: ErrorResponse) => {
    switch (error.status) {
      case 403:
        setError('权限不足：只有领导角色可以查看评估进度');
        showError('权限不足', '请联系管理员分配领导角色');
        break;
      case 400:
        if (error.message.includes('未分配到任何部门')) {
          setError('部门信息缺失：请联系HR完成部门分配');
          showError('部门信息缺失', '请联系HR完成部门分配');
        } else {
          setError('权限不足：仅限领导角色访问');
          showError('权限不足', '请联系管理员分配相应权限');
        }
        break;
      default:
        setError('获取评估进度失败');
        showError('系统错误', '请稍后重试');
    }
  };

  useEffect(() => {
    const fetchProgress = async () => {
      try {
        setLoading(true);
        const response = await api.get(`/evaluations/progress/${assessmentId}`);
        setData(response.data);
      } catch (err) {
        handleApiError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchProgress();
  }, [assessmentId]);

  return { data, loading, error };
};
```

### 5. UI组件更新

更新进度显示组件，明确显示数据范围：

```typescript
// components/evaluations/ProgressDashboard.tsx
import React from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useEvaluationProgress } from '@/hooks/useEvaluationProgress';

interface Props {
  assessmentId: number;
}

export const ProgressDashboard: React.FC<Props> = ({ assessmentId }) => {
  const { user } = useAuth();
  const { data, loading, error } = useEvaluationProgress(assessmentId);

  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage message={error} />;

  return (
    <div className="progress-dashboard">
      {/* 明确标示数据范围 */}
      <div className="dashboard-header">
        <h2>{data.assessment_title}</h2>
        <div className="scope-indicator">
          <span className="department-badge">
            📍 {user.department.name} 部门进度
          </span>
          <span className="total-count">
            总计 {data.total_participants} 人
          </span>
        </div>
      </div>

      {/* 进度统计卡片 */}
      <div className="progress-cards">
        <ProgressCard
          title="自评完成"
          count={data.self_completed_count}
          total={data.total_participants}
          percentage={data.self_completion_rate}
        />
        <ProgressCard
          title="领导评分完成"
          count={data.leader_completed_count}
          total={data.total_participants}
          percentage={data.leader_completion_rate}
        />
        <ProgressCard
          title="上级评分完成"
          count={data.boss_completed_count}
          total={data.total_participants}
          percentage={data.boss_completion_rate}
        />
      </div>

      {/* 参与者详情表格 */}
      <ParticipantsTable participants={data.participants} />
    </div>
  );
};
```

---

## 🔄 为受影响角色提供替代方案

### 方案1: Admin 全局视图 (推荐)

为管理员提供新的全局统计接口：

```typescript
// 新接口建议 - 需要后端实现
GET /api/v1/evaluations/progress-summary/{assessmentId}

// 响应结构
interface ProgressSummaryDto {
  assessment_id: number;
  assessment_title: string;
  total_departments: number;
  total_participants: number;
  overall_completion_rate: number;
  departments: Array<{
    department_id: number;
    department_name: string;
    participants_count: number;
    completion_rate: number;
    leader_name: string;
  }>;
}
```

前端调用示例：

```typescript
// hooks/useProgressSummary.ts - 专门为Admin使用
export const useProgressSummary = (assessmentId: number) => {
  const { user } = useAuth();
  
  const fetchSummary = async () => {
    if (!user.roles.includes('admin')) {
      throw new Error('仅管理员可以查看全局进度摘要');
    }
    
    const response = await api.get(`/evaluations/progress-summary/${assessmentId}`);
    return response.data;
  };
  
  return useQuery(['progress-summary', assessmentId], fetchSummary);
};
```

### 方案2: Boss 管理范围视图

为高层管理者提供基于管理范围的数据：

```typescript
// 新接口建议 - 需要后端实现
GET /api/v1/evaluations/progress-by-scope/{assessmentId}

// 根据当前用户的管理权限返回相应范围的数据
// Boss角色可能管理多个部门，返回所有管辖部门的数据
```

---

## 📝 文案和提示更新

### 1. 页面标题和描述

```typescript
// 更新前端文案
const PAGE_TITLES = {
  // 旧文案
  old: "全公司评估进度",
  // 新文案
  new: "部门评估进度"
};

const DESCRIPTIONS = {
  old: "查看所有员工的评估完成情况",
  new: "查看本部门员工的评估完成情况"
};
```

### 2. 搜索和筛选提示

```typescript
// 更新搜索框占位符
const SEARCH_PLACEHOLDERS = {
  old: "搜索员工姓名或工号...",
  new: "搜索本部门员工..."
};
```

### 3. 帮助文档更新

```typescript
// 添加权限说明
const HELP_TEXT = `
📋 权限说明：
• 只有具备领导角色的用户可以查看评估进度
• 显示数据范围限定为您所在的部门
• 如需查看其他部门数据，请联系管理员

📞 需要帮助？
• 权限问题：联系IT管理员
• 部门分配：联系HR部门
`;
```

---

## 🧪 测试建议

### 1. 权限测试用例

```typescript
// tests/evaluationProgress.test.ts
describe('Evaluation Progress API', () => {
  test('Leader role can access department progress', async () => {
    const leaderToken = await getTokenForRole('leader');
    const response = await api.get('/evaluations/progress/1', {
      headers: { Authorization: `Bearer ${leaderToken}` }
    });
    expect(response.status).toBe(200);
  });

  test('Admin role receives 403 Forbidden', async () => {
    const adminToken = await getTokenForRole('admin');
    await expect(
      api.get('/evaluations/progress/1', {
        headers: { Authorization: `Bearer ${adminToken}` }
      })
    ).rejects.toMatchObject({ status: 403 });
  });

  test('Boss role receives 403 Forbidden', async () => {
    const bossToken = await getTokenForRole('boss');
    await expect(
      api.get('/evaluations/progress/1', {
        headers: { Authorization: `Bearer ${bossToken}` }
      })
    ).rejects.toMatchObject({ status: 403 });
  });

  test('Leader without department receives 400 error', async () => {
    const leaderWithoutDeptToken = await getTokenForUserWithoutDept('leader');
    await expect(
      api.get('/evaluations/progress/1', {
        headers: { Authorization: `Bearer ${leaderWithoutDeptToken}` }
      })
    ).rejects.toMatchObject({ 
      status: 400,
      message: expect.stringContaining('未分配到任何部门')
    });
  });
});
```

### 2. UI测试用例

```typescript
// tests/components/ProgressDashboard.test.tsx
describe('ProgressDashboard Component', () => {
  test('Shows permission denied for non-leader users', () => {
    const nonLeaderUser = { roles: ['employee'], department: null };
    render(
      <AuthProvider user={nonLeaderUser}>
        <ProgressDashboard assessmentId={1} />
      </AuthProvider>
    );
    expect(screen.getByText('权限不足')).toBeInTheDocument();
  });

  test('Shows department required for leader without department', () => {
    const leaderWithoutDept = { roles: ['leader'], department: null };
    render(
      <AuthProvider user={leaderWithoutDept}>
        <ProgressDashboard assessmentId={1} />
      </AuthProvider>
    );
    expect(screen.getByText('部门信息缺失')).toBeInTheDocument();
  });

  test('Displays department name in progress title', () => {
    const leaderUser = { 
      roles: ['leader'], 
      department: { id: 1, name: '技术部' }
    };
    render(
      <AuthProvider user={leaderUser}>
        <ProgressDashboard assessmentId={1} />
      </AuthProvider>
    );
    expect(screen.getByText('技术部 部门进度')).toBeInTheDocument();
  });
});
```

---

## ⚡ 实施时间线

### 第一阶段 (立即) - 紧急修复
- [ ] 添加权限检查组件
- [ ] 更新路由配置
- [ ] 修复Admin和Boss页面的403错误
- [ ] 更新错误处理逻辑

### 第二阶段 (本周内) - 体验优化
- [ ] 更新UI文案和提示
- [ ] 优化错误页面设计
- [ ] 添加权限说明和帮助文档
- [ ] 更新导航菜单显示逻辑

### 第三阶段 (下个迭代) - 功能增强
- [ ] 开发Admin全局统计功能
- [ ] 实现Boss管理范围视图
- [ ] 添加数据导出功能
- [ ] 完善权限管理界面

---

## 📞 联系方式

**前端团队负责人**: 
- 技术问题: 联系前端架构师
- 产品问题: 联系产品经理

**后端支持**:
- API问题: 联系后端开发团队
- 权限配置: 联系系统管理员

**紧急联系**:
- 如果生产环境出现问题，请立即联系值班人员

---

*最后更新: 2025-07-25*  
*文档版本: v1.0*