# API变更摘要 - 评估进度端点安全性增强

## 📝 变更概述

**版本**: v2.1.0  
**日期**: 2025-07-25  
**类型**: 安全性增强 + 破坏性变更  
**影响范围**: 前端UI、权限系统

## 🔄 变更内容

### 修改的端点
- `GET /api/v1/evaluations/progress/{assessmentId}`

### 权限变更
| 项目 | 变更前 | 变更后 |
|------|--------|--------|
| 允许角色 | `leader`, `boss`, `admin` | **仅** `leader` |
| 数据范围 | 所有参与者 | 仅当前领导部门的参与者 |
| 部门要求 | 无要求 | 必须分配部门 |

### 新增错误响应
- `400 Bad Request`: 用户未分配部门
- `403 Forbidden`: 非leader角色访问

## 🚨 破坏性影响

### 前端组件
- ❌ Admin管理后台的评估进度页面将返回403错误
- ❌ Boss高层仪表板的跨部门数据获取失败
- ⚠️ 进度统计和图表数据范围变为部门级别

### 用户角色
- ❌ `admin`角色失去评估进度查看权限
- ❌ `boss`角色失去评估进度查看权限
- ✅ `leader`角色功能不受影响（数据范围变小）

## 🔧 前端修复要求

### 必须修改 (P0)
1. **权限检查**: 所有相关组件添加leader角色检查
2. **错误处理**: 处理403和400错误响应
3. **路由保护**: 评估进度路由限制为leader角色
4. **导航菜单**: 条件显示评估进度菜单项

### 建议修改 (P1)
1. **UI文案**: 更新为"部门进度"相关描述
2. **帮助文档**: 添加权限和数据范围说明
3. **错误页面**: 设计友好的权限错误页面

### 功能增强 (P2)
1. **Admin替代方案**: 开发全局统计功能
2. **Boss替代方案**: 基于管理范围的数据聚合
3. **权限管理**: 优化权限分配界面

## 📋 验收清单

### 功能测试
- [ ] Leader可以正常查看部门评估进度
- [ ] Admin访问时显示权限错误
- [ ] Boss访问时显示权限错误
- [ ] 未分配部门的Leader显示部门错误

### UI测试
- [ ] 页面标题显示为"部门进度"
- [ ] 数据范围说明准确
- [ ] 错误页面友好美观
- [ ] 导航菜单正确显示/隐藏

## 🔄 回滚计划

如果出现严重问题，可以通过以下步骤回滚：

1. **代码回滚**: 恢复原有权限配置
2. **重新部署**: 部署到生产环境
3. **验证功能**: 确认所有角色都能正常访问

## 📞 联系方式

**技术问题**: 开发团队群  
**产品问题**: 产品经理  
**紧急联系**: 项目负责人

---

## 📚 相关文档

- [详细前端集成指南](./EVALUATION_PROGRESS_API_CHANGES.md)
- [前端检查清单](./FRONTEND_INTEGRATION_CHECKLIST.md)
- [安全性测试报告](./evaluation-progress-security-test.md)

---

*摘要版本: v1.0*  
*最后更新: 2025-07-25*