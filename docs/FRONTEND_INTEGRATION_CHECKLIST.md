# 前端集成检查清单 - 评估进度API修改

## 🔍 快速自检清单

请前端团队按照此清单检查代码，确保所有相关组件都已适配新的API权限要求。

### ✅ 权限检查

- [ ] **路由级权限**: 检查评估进度相关路由是否限制为leader角色
- [ ] **组件级权限**: 确认所有调用进度API的组件都有权限检查
- [ ] **菜单显示**: 导航菜单中的"评估进度"项只对leader角色显示
- [ ] **按钮禁用**: 相关操作按钮根据权限状态禁用

### ✅ 错误处理

- [ ] **403错误**: 处理非leader角色访问时的403 Forbidden错误
- [ ] **400错误**: 处理未分配部门用户的400 Bad Request错误
- [ ] **用户友好提示**: 错误信息提供明确的解决方案指引
- [ ] **错误页面**: 设计专门的权限不足和部门缺失错误页面

### ✅ UI文案更新

- [ ] **页面标题**: 从"全公司进度"改为"部门进度"
- [ ] **搜索提示**: 搜索框占位符更新为"搜索本部门员工"
- [ ] **数据范围说明**: 明确标注显示的是部门范围数据
- [ ] **帮助文档**: 更新相关帮助说明和FAQ

### ✅ 功能适配

- [ ] **数据解读**: 统计图表组件理解数据范围变化
- [ ] **分页逻辑**: 确认分页组件适应数据量变化
- [ ] **导出功能**: 更新数据导出的文件名和说明
- [ ] **打印功能**: 调整打印页面的标题和说明

---

## 🔧 关键文件清单

### 需要修改的组件文件

```
src/
├── components/
│   ├── evaluation/
│   │   ├── ProgressDashboard.tsx        # 🔴 主要组件，需要权限检查
│   │   ├── ProgressChart.tsx            # 🔴 图表组件，需要更新标题
│   │   ├── ParticipantsTable.tsx       # 🟡 表格组件，数据范围变化
│   │   └── ProgressExport.tsx           # 🟡 导出组件，更新文件名
│   ├── navigation/
│   │   └── NavigationMenu.tsx           # 🔴 导航菜单，需要条件渲染
│   └── guards/
│       └── EvaluationProgressGuard.tsx  # 🆕 新增权限守卫组件
├── hooks/
│   ├── useEvaluationProgress.ts         # 🔴 数据获取hook，需要错误处理
│   └── useAuth.ts                       # 🟡 可能需要扩展权限检查方法
├── pages/
│   ├── EvaluationProgressPage.tsx       # 🔴 页面组件，需要权限包装
│   └── PermissionDeniedPage.tsx         # 🆕 新增权限错误页面
└── router/
    └── evaluationRoutes.tsx             # 🔴 路由配置，需要权限守卫
```

**图例**:
- 🔴 必须修改
- 🟡 建议检查
- 🆕 需要新建

### 配置文件

- [ ] **权限配置**: `src/config/permissions.ts`
- [ ] **路由配置**: `src/router/index.ts`
- [ ] **API配置**: `src/api/endpoints.ts`

---

## 🧪 快速测试方案

### 1. 手动测试步骤

```bash
# 1. 以leader角色登录
# 2. 访问评估进度页面，确认正常显示
# 3. 以admin角色登录
# 4. 访问评估进度页面，确认显示权限错误
# 5. 以boss角色登录
# 6. 访问评估进度页面，确认显示权限错误
```

### 2. 自动化测试

```typescript
// 在现有测试文件中添加
describe('Evaluation Progress Access Control', () => {
  const testCases = [
    { role: 'leader', shouldAccess: true },
    { role: 'admin', shouldAccess: false },
    { role: 'boss', shouldAccess: false },
    { role: 'employee', shouldAccess: false }
  ];

  testCases.forEach(({ role, shouldAccess }) => {
    test(`${role} role access`, async () => {
      const user = await createUserWithRole(role, { department: mockDepartment });
      const component = render(<ProgressDashboard />, { user });
      
      if (shouldAccess) {
        expect(component.queryByText('权限不足')).not.toBeInTheDocument();
      } else {
        expect(component.getByText('权限不足')).toBeInTheDocument();
      }
    });
  });
});
```

---

## 🚨 紧急问题排查

### 问题1: Admin无法访问评估进度
**症状**: 管理员登录后访问评估进度页面显示403错误  
**解决**: 这是预期行为，需要为Admin开发新的全局统计功能

### 问题2: Leader看不到数据
**症状**: Leader角色用户页面空白或显示"暂无数据"  
**排查**:
1. 检查用户是否分配了部门
2. 检查该部门是否有参与考核的员工
3. 检查API调用是否正常

### 问题3: 页面一直加载中
**症状**: 进度页面显示loading状态不结束  
**排查**:
1. 检查网络请求是否返回错误
2. 检查错误处理逻辑是否正确
3. 查看浏览器控制台错误信息

---

## 📋 验收标准

### 功能验收
- [ ] Leader角色可以正常查看本部门评估进度
- [ ] 非Leader角色显示权限错误提示
- [ ] 未分配部门的Leader显示部门缺失提示
- [ ] 错误页面提供明确的解决方案指引

### 用户体验验收
- [ ] 页面标题和说明文案准确反映数据范围
- [ ] 错误提示友好且具有指导性
- [ ] 权限不足时的页面设计美观
- [ ] 帮助文档内容完整且易懂

### 技术验收
- [ ] 所有相关组件都有适当的权限检查
- [ ] 错误处理覆盖所有异常情况
- [ ] 代码符合项目规范和最佳实践
- [ ] 自动化测试覆盖主要场景

---

## 📞 获得帮助

**技术支持**: 前端技术群  
**产品咨询**: 产品经理  
**紧急问题**: 值班电话

---

*检查清单版本: v1.0*  
*创建时间: 2025-07-25*