# OKR服务器管理脚本

本目录包含用于OKR服务器部署和维护的shell脚本。

## 脚本列表

### 1. update.sh - 完整更新脚本
用于完整的服务器更新，包括代码拉取、依赖安装、数据库迁移等。

**功能特性:**
- 自动备份当前版本
- 拉取最新代码
- 安装/更新依赖
- 运行数据库迁移
- 构建项目
- 重启服务
- 验证服务状态
- 清理旧备份

**使用方法:**
```bash
# 给脚本添加执行权限
chmod +x sh/update.sh

# 运行更新脚本
./sh/update.sh
```

### 2. quick-update.sh - 快速更新脚本
用于仅涉及代码更改的快速更新，不包含依赖安装和数据库迁移。

**适用场景:**
- 纯代码修改
- 不涉及package.json变更
- 不涉及数据库结构变更

**使用方法:**
```bash
chmod +x sh/quick-update.sh
./sh/quick-update.sh
```

### 3. rollback.sh - 回滚脚本
用于回滚到之前的备份版本。

**功能特性:**
- 显示可用备份列表
- 支持选择指定备份进行回滚
- 回滚前自动备份当前状态
- 验证回滚后服务状态

**使用方法:**
```bash
chmod +x sh/rollback.sh
./sh/rollback.sh
```

## 使用建议

### 日常更新流程
1. **小型修改** (如UI调整、逻辑优化)
   ```bash
   ./sh/quick-update.sh
   ```

2. **中大型更新** (如新功能、依赖更新)
   ```bash
   ./sh/update.sh
   ```

3. **紧急回滚**
   ```bash
   ./sh/rollback.sh
   ```

### 安全建议
1. 在生产环境更新前，先在测试环境验证
2. 重要更新前手动创建额外备份
3. 监控服务状态和日志输出
4. 准备回滚方案

### 备份管理
- 脚本会自动创建备份到 `backups/` 目录
- 备份目录格式: `YYYYMMDD_HHMMSS`
- 自动清理7天前的备份
- 手动备份建议存放到其他安全位置

## 服务器要求

### 必需软件
- Git
- Node.js 18+
- PM2
- MySQL/MariaDB

### 权限要求
- 脚本需要在项目根目录运行
- 需要对项目目录的读写权限
- 需要操作PM2进程的权限

## 故障排除

### 常见问题

**1. 权限不足**
```bash
chmod +x sh/*.sh
```

**2. PM2未安装**
```bash
npm install -g pm2
```

**3. 服务启动失败**
检查日志:
```bash
pm2 logs okr-server
```

**4. 端口冲突**
检查端口占用:
```bash
netstat -tlnp | grep :3010
```

### 日志位置
- PM2日志: `./logs/`
- 应用日志: 应用内部日志系统
- 更新日志: 终端输出

## 联系支持

如遇到问题，请联系技术支持团队或查看部署文档:
- 部署文档: `docs/deployment-guide.md`
- 项目文档: `README.md`